package dal

import (
	"fmt"
	"log"
	"os"
	"t66y/model/entity"
	"time"

	"go.uber.org/zap"
	"gorm.io/driver/postgres"
	"gorm.io/gorm"
	"gorm.io/gorm/logger"
)

var (
	DB *gorm.DB
)

func dbMigrate() {
	db := DB.Session(&gorm.Session{
		Logger: DB.Logger.LogMode(logger.Warn),
	})
	err := db.AutoMigrate(
		&entity.Post{},
	)
	if err != nil {
		log.Fatal("failed auto migrate db", zap.Error(err))
	}
}

func NewGormDB() *gorm.DB {
	newLogger := logger.New(
		log.New(os.Stdout, "\r\n", log.LstdFlags), // io writer
		logger.Config{
			SlowThreshold:             30 * time.Second, // Slow SQL threshold
			LogLevel:                  logger.Warn,      // Log level
			IgnoreRecordNotFoundError: true,             // Ignore ErrRecordNotFound error for logger
			ParameterizedQueries:      true,             // Don't include params in the SQL log
			Colorful:                  false,            // Disable color
		},
	)
	dsn := fmt.Sprintf("***************************************************/nione")
	DB, _ = gorm.Open(postgres.Open(dsn), &gorm.Config{
		Logger: newLogger,
	})
	sqlDB, err := DB.DB()
	if err != nil {
		log.Fatal("get database connection error")
	}
	sqlDB.SetMaxIdleConns(500)
	sqlDB.SetMaxOpenConns(1000)
	sqlDB.SetConnMaxIdleTime(time.Hour)
	SetDefault(DB)
	//dbMigrate()
	return DB
}
