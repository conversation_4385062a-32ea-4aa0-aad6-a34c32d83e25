// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package dal

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"t66y/model/entity"
)

func newPost(db *gorm.DB, opts ...gen.DOOption) post {
	_post := post{}

	_post.postDo.UseDB(db, opts...)
	_post.postDo.UseModel(&entity.Post{})

	tableName := _post.postDo.TableName()
	_post.ALL = field.NewAsterisk(tableName)
	_post.ID = field.NewInt32(tableName, "id")
	_post.Title = field.NewString(tableName, "title")
	_post.CreatedAt = field.NewTime(tableName, "created_at")
	_post.PostID = field.NewString(tableName, "post_id")
	_post.Author = field.NewString(tableName, "author")
	_post.MessageID = field.NewInt32(tableName, "message_id")
	_post.Content = field.NewString(tableName, "content")
	_post.URL = field.NewString(tableName, "url")
	_post.TgURL = field.NewString(tableName, "tg_url")
	_post.UpdatedAt = field.NewTime(tableName, "updated_at")
	_post.DeletedAt = field.NewField(tableName, "deleted_at")

	_post.fillFieldMap()

	return _post
}

type post struct {
	postDo

	ALL       field.Asterisk
	ID        field.Int32
	Title     field.String
	CreatedAt field.Time
	PostID    field.String
	Author    field.String
	MessageID field.Int32
	Content   field.String
	URL       field.String
	TgURL     field.String
	UpdatedAt field.Time
	DeletedAt field.Field

	fieldMap map[string]field.Expr
}

func (p post) Table(newTableName string) *post {
	p.postDo.UseTable(newTableName)
	return p.updateTableName(newTableName)
}

func (p post) As(alias string) *post {
	p.postDo.DO = *(p.postDo.As(alias).(*gen.DO))
	return p.updateTableName(alias)
}

func (p *post) updateTableName(table string) *post {
	p.ALL = field.NewAsterisk(table)
	p.ID = field.NewInt32(table, "id")
	p.Title = field.NewString(table, "title")
	p.CreatedAt = field.NewTime(table, "created_at")
	p.PostID = field.NewString(table, "post_id")
	p.Author = field.NewString(table, "author")
	p.MessageID = field.NewInt32(table, "message_id")
	p.Content = field.NewString(table, "content")
	p.URL = field.NewString(table, "url")
	p.TgURL = field.NewString(table, "tg_url")
	p.UpdatedAt = field.NewTime(table, "updated_at")
	p.DeletedAt = field.NewField(table, "deleted_at")

	p.fillFieldMap()

	return p
}

func (p *post) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := p.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (p *post) fillFieldMap() {
	p.fieldMap = make(map[string]field.Expr, 11)
	p.fieldMap["id"] = p.ID
	p.fieldMap["title"] = p.Title
	p.fieldMap["created_at"] = p.CreatedAt
	p.fieldMap["post_id"] = p.PostID
	p.fieldMap["author"] = p.Author
	p.fieldMap["message_id"] = p.MessageID
	p.fieldMap["content"] = p.Content
	p.fieldMap["url"] = p.URL
	p.fieldMap["tg_url"] = p.TgURL
	p.fieldMap["updated_at"] = p.UpdatedAt
	p.fieldMap["deleted_at"] = p.DeletedAt
}

func (p post) clone(db *gorm.DB) post {
	p.postDo.ReplaceConnPool(db.Statement.ConnPool)
	return p
}

func (p post) replaceDB(db *gorm.DB) post {
	p.postDo.ReplaceDB(db)
	return p
}

type postDo struct{ gen.DO }

func (p postDo) Debug() *postDo {
	return p.withDO(p.DO.Debug())
}

func (p postDo) WithContext(ctx context.Context) *postDo {
	return p.withDO(p.DO.WithContext(ctx))
}

func (p postDo) ReadDB() *postDo {
	return p.Clauses(dbresolver.Read)
}

func (p postDo) WriteDB() *postDo {
	return p.Clauses(dbresolver.Write)
}

func (p postDo) Session(config *gorm.Session) *postDo {
	return p.withDO(p.DO.Session(config))
}

func (p postDo) Clauses(conds ...clause.Expression) *postDo {
	return p.withDO(p.DO.Clauses(conds...))
}

func (p postDo) Returning(value interface{}, columns ...string) *postDo {
	return p.withDO(p.DO.Returning(value, columns...))
}

func (p postDo) Not(conds ...gen.Condition) *postDo {
	return p.withDO(p.DO.Not(conds...))
}

func (p postDo) Or(conds ...gen.Condition) *postDo {
	return p.withDO(p.DO.Or(conds...))
}

func (p postDo) Select(conds ...field.Expr) *postDo {
	return p.withDO(p.DO.Select(conds...))
}

func (p postDo) Where(conds ...gen.Condition) *postDo {
	return p.withDO(p.DO.Where(conds...))
}

func (p postDo) Order(conds ...field.Expr) *postDo {
	return p.withDO(p.DO.Order(conds...))
}

func (p postDo) Distinct(cols ...field.Expr) *postDo {
	return p.withDO(p.DO.Distinct(cols...))
}

func (p postDo) Omit(cols ...field.Expr) *postDo {
	return p.withDO(p.DO.Omit(cols...))
}

func (p postDo) Join(table schema.Tabler, on ...field.Expr) *postDo {
	return p.withDO(p.DO.Join(table, on...))
}

func (p postDo) LeftJoin(table schema.Tabler, on ...field.Expr) *postDo {
	return p.withDO(p.DO.LeftJoin(table, on...))
}

func (p postDo) RightJoin(table schema.Tabler, on ...field.Expr) *postDo {
	return p.withDO(p.DO.RightJoin(table, on...))
}

func (p postDo) Group(cols ...field.Expr) *postDo {
	return p.withDO(p.DO.Group(cols...))
}

func (p postDo) Having(conds ...gen.Condition) *postDo {
	return p.withDO(p.DO.Having(conds...))
}

func (p postDo) Limit(limit int) *postDo {
	return p.withDO(p.DO.Limit(limit))
}

func (p postDo) Offset(offset int) *postDo {
	return p.withDO(p.DO.Offset(offset))
}

func (p postDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *postDo {
	return p.withDO(p.DO.Scopes(funcs...))
}

func (p postDo) Unscoped() *postDo {
	return p.withDO(p.DO.Unscoped())
}

func (p postDo) Create(values ...*entity.Post) error {
	if len(values) == 0 {
		return nil
	}
	return p.DO.Create(values)
}

func (p postDo) CreateInBatches(values []*entity.Post, batchSize int) error {
	return p.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (p postDo) Save(values ...*entity.Post) error {
	if len(values) == 0 {
		return nil
	}
	return p.DO.Save(values)
}

func (p postDo) First() (*entity.Post, error) {
	if result, err := p.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*entity.Post), nil
	}
}

func (p postDo) Take() (*entity.Post, error) {
	if result, err := p.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*entity.Post), nil
	}
}

func (p postDo) Last() (*entity.Post, error) {
	if result, err := p.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*entity.Post), nil
	}
}

func (p postDo) Find() ([]*entity.Post, error) {
	result, err := p.DO.Find()
	return result.([]*entity.Post), err
}

func (p postDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*entity.Post, err error) {
	buf := make([]*entity.Post, 0, batchSize)
	err = p.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (p postDo) FindInBatches(result *[]*entity.Post, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return p.DO.FindInBatches(result, batchSize, fc)
}

func (p postDo) Attrs(attrs ...field.AssignExpr) *postDo {
	return p.withDO(p.DO.Attrs(attrs...))
}

func (p postDo) Assign(attrs ...field.AssignExpr) *postDo {
	return p.withDO(p.DO.Assign(attrs...))
}

func (p postDo) Joins(fields ...field.RelationField) *postDo {
	for _, _f := range fields {
		p = *p.withDO(p.DO.Joins(_f))
	}
	return &p
}

func (p postDo) Preload(fields ...field.RelationField) *postDo {
	for _, _f := range fields {
		p = *p.withDO(p.DO.Preload(_f))
	}
	return &p
}

func (p postDo) FirstOrInit() (*entity.Post, error) {
	if result, err := p.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*entity.Post), nil
	}
}

func (p postDo) FirstOrCreate() (*entity.Post, error) {
	if result, err := p.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*entity.Post), nil
	}
}

func (p postDo) FindByPage(offset int, limit int) (result []*entity.Post, count int64, err error) {
	result, err = p.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = p.Offset(-1).Limit(-1).Count()
	return
}

func (p postDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = p.Count()
	if err != nil {
		return
	}

	err = p.Offset(offset).Limit(limit).Scan(result)
	return
}

func (p postDo) Scan(result interface{}) (err error) {
	return p.DO.Scan(result)
}

func (p postDo) Delete(models ...*entity.Post) (result gen.ResultInfo, err error) {
	return p.DO.Delete(models)
}

func (p *postDo) withDO(do gen.Dao) *postDo {
	p.DO = *do.(*gen.DO)
	return p
}
