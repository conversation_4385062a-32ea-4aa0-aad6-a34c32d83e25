package main

import (
	"encoding/json"
	"fmt"
	"github.com/meinside/telegraph-go"
	"regexp"
	"strconv"
	"strings"
	"t66y/dal"
	"t66y/model/entity"
	"time"

	"github.com/PuerkitoBio/goquery"
	"github.com/imroc/req/v3"
	"github.com/mymmrac/telego"
	tu "github.com/mymmrac/telego/telegoutil"
	"github.com/spf13/cast"
)

var (
	bot          *telego.Bot
	botToken     = "7972400064:AAH8d9HSHam12olitMdV_btLIn9XO_MfImY"
	channelID    = int64(-1002369927444) // 频道ID
	discussionID = int64(-1002875038292) // 讨论群组ID，需要修改为实际的讨论群组ID
	domain       = "https://www.lsmao.org"

	// 登录信息
	username = "wtfmao"           // 网站用户名，需要填写
	password = "Aa998877@@123123" // 网站密码，需要填写

	http = req.C().
		ImpersonateChrome()

	// 控制消息发送形式的变量
	// true: 使用Telegraph帖子形式（图片在Telegraph页面中查看）
	// false: 使用评论形式（图片发送到讨论群组的评论中）
	useTelegraphPost = true
)

func handlePost(title string, postId string, url string, thumbnailUrl string) {
	fmt.Printf("正在访问帖子URL: %s\n", url)
	resp, err := http.R().Get(url)
	if err != nil {
		fmt.Printf("❌ 访问帖子页面失败: %v\n", err)
		return
	}
	fmt.Printf("帖子页面HTTP状态码: %d\n", resp.StatusCode)

	author, content, coverImage, keywords, imageCount := PureHtml(resp.Body)
	if content == nil {
		fmt.Println("无法解析帖子内容，跳过处理")
		return
	}
	fmt.Println("title: ", title)
	fmt.Println("author: ", author)
	fmt.Printf("解析到 %d 个内容节点\n", len(content))

	// 提取图片链接
	imageLinks := extractImageLinks(content)
	fmt.Printf("找到 %d 张图片\n", len(imageLinks))

	// 如果没有图片，跳过处理
	if len(imageLinks) == 0 {
		fmt.Printf("该帖子没有图片，跳过发送\n")
		return
	}

	var titleMsg *telego.Message
	var telegraphURL string // 用于存储Telegraph页面URL

	if useTelegraphPost {
		// 使用Telegraph帖子形式
		fmt.Printf("📰 创建Telegraph帖子...\n")
		// 在标题中添加图片数量
		titleWithCount := fmt.Sprintf("%s[%dP]", title, imageCount)
		page, err := createTelegraphPage(titleWithCount, author, content)
		if err != nil {
			fmt.Printf("❌ 创建Telegraph页面失败: %v\n", err.Error())
			return
		}
		telegraphURL = page.URL // 保存Telegraph页面URL

		// 发送Telegraph链接到频道（依靠Telegram即时预览）
		titleMessage := fmt.Sprintf("%s\n\n%s", keywords, page.URL)

		for retry := 0; retry < 3; retry++ {
			// 只发送文本消息，让Telegram自动生成Telegraph页面的即时预览
			fmt.Printf("📝 发送Telegraph链接，依靠TG即时预览\n")
			titleMsg, err = bot.SendMessage(
				tu.Message(
					tu.ID(channelID),
					titleMessage,
				),
			)

			if err == nil {
				break
			}
			fmt.Printf("发送消息失败 (重试 %d/3): %v\n", retry+1, err.Error())
			if retry < 2 {
				time.Sleep(3 * time.Second)
			}
		}

		if err != nil {
			fmt.Printf("❌ 发送消息最终失败: %v\n", err.Error())
			return
		}
		fmt.Printf("✅ 成功发送Telegraph帖子到频道，消息ID: %d\n", titleMsg.MessageID)

	} else {
		// 使用评论形式
		titleMessage := fmt.Sprintf(" %s[%dP]\n👤 作者：#%s\n🏷️ 关键词：%s", title, imageCount, author, keywords)
		fmt.Printf("发送消息到频道...\n")

		for retry := 0; retry < 3; retry++ {
			// 优先使用缩略图，如果没有则使用详情页第一张图片
			finalCoverImage := thumbnailUrl
			if finalCoverImage == "" {
				finalCoverImage = coverImage
			}

			if finalCoverImage != "" {
				// 发送带封面图片的消息
				fmt.Printf("📷 使用封面图片: %s\n", finalCoverImage)
				titleMsg, err = bot.SendPhoto(
					tu.Photo(
						tu.ID(channelID),
						tu.FileFromURL(finalCoverImage),
					).WithCaption(titleMessage),
				)
			} else {
				// 没有封面图片时发送普通文本消息
				fmt.Printf("📝 没有封面图片，发送文本消息\n")
				titleMsg, err = bot.SendMessage(
					tu.Message(
						tu.ID(channelID),
						titleMessage,
					),
				)
			}

			if err == nil {
				break
			}
			fmt.Printf("发送消息失败 (重试 %d/3): %v\n", retry+1, err.Error())
			if retry < 2 {
				time.Sleep(3 * time.Second)
			}
		}

		if err != nil {
			fmt.Printf("❌ 发送消息最终失败: %v\n", err.Error())
			return
		}
		fmt.Printf("✅ 成功发送消息到频道，消息ID: %d\n", titleMsg.MessageID)

		// 如果配置了讨论群组，在群组中回复
		if discussionID != channelID {
			fmt.Printf("在讨论群组中发送图片...\n")

			// 等待5秒让频道消息同步到群组，避免频率限制
			fmt.Printf("⏰ 等待5秒让频道消息同步到群组...\n")
			time.Sleep(5 * time.Second)

			// 尝试找到群组中对应的消息ID
			groupMessageID := findCorrespondingMessage(titleMsg.MessageID)

			if groupMessageID > 0 {
				fmt.Printf("✅ 找到群组中对应的消息ID: %d\n", groupMessageID)
				sendImagesToDiscussion(imageLinks, groupMessageID)
			} else {
				fmt.Printf("❌ 无法找到群组中对应的消息，直接发送MediaGroup到群组\n")
				sendImagesToDiscussion(imageLinks, 0) // 使用0表示不回复任何消息
			}
		} else {
			fmt.Printf("⚠️ 讨论群组未配置，跳过图片发送\n")
		}
	}

	msgId := cast.ToInt32(titleMsg.MessageID)
	postContent, _ := json.Marshal(content)
	postContentString := string(postContent)
	dal.Post.Create(&entity.Post{
		PostID:    postId,
		MessageID: msgId,
		Title:     title,
		Author:    author,
		Content:   postContentString,
		URL:       url,
		TgURL:     telegraphURL, // 保存Telegraph页面URL
	})

}

// getMaxPageNumber 获取最大页码数
func getMaxPageNumber() int {
	fmt.Printf("🔍 获取最大页码数...\n")

	// 访问第一页获取页码信息
	resp, err := http.R().Get(domain + "/forum-40-1.html")
	if err != nil {
		fmt.Printf("❌ 获取页码信息失败: %v\n", err)
		return 5 // 返回默认值
	}

	doc, err := goquery.NewDocumentFromReader(resp.Body)
	if err != nil {
		fmt.Printf("❌ 解析页码HTML失败: %v\n", err)
		return 5 // 返回默认值
	}

	maxPage := 1

	// 查找分页区域的所有文本
	pageTexts := []string{}
	
	// 查找分页链接
	doc.Find("a").Each(func(i int, s *goquery.Selection) {
		href, exists := s.Attr("href")
		if !exists {
			return
		}

		// 提取页码，例如: forum-40-353.html
		re := regexp.MustCompile(`forum-40-(\d+)\.html`)
		matches := re.FindStringSubmatch(href)
		if len(matches) >= 2 {
			if pageNum, err := strconv.Atoi(matches[1]); err == nil {
				if pageNum > maxPage {
					maxPage = pageNum
				}
			}
		}
	})

	// 查找页面文本中的 "数字 / 数字 页" 格式
	doc.Find("*").Each(func(i int, s *goquery.Selection) {
		text := strings.TrimSpace(s.Text())
		pageTexts = append(pageTexts, text)
	})

	// 解析所有文本，查找 "353 / 353 页" 这样的格式
	for _, text := range pageTexts {
		// 跳过空文本和过短的文本
		if len(text) < 3 {
			continue
		}
		
		// 匹配 "353 / 353 页" 格式
		re := regexp.MustCompile(`(\d+)\s*/\s*(\d+)\s*页`)
		matches := re.FindStringSubmatch(text)
		if len(matches) >= 3 {
			if pageNum, err := strconv.Atoi(matches[2]); err == nil {
				if pageNum > maxPage {
					maxPage = pageNum
					fmt.Printf("📄 从页面文本中找到最大页码: %s -> %d\n", text, pageNum)
				}
			}
		}

		// 也匹配简单的 "... 353" 格式
		re2 := regexp.MustCompile(`\.\.\.\s*(\d+)`)
		matches2 := re2.FindStringSubmatch(text)
		if len(matches2) >= 2 {
			if pageNum, err := strconv.Atoi(matches2[1]); err == nil {
				if pageNum > maxPage {
					maxPage = pageNum
					fmt.Printf("📄 从省略号格式中找到最大页码: %s -> %d\n", text, pageNum)
				}
			}
		}
		
		// 匹配 "**1**2345678910... 353 / 353 页下一页" 这种格式
		re3 := regexp.MustCompile(`(\d+)\s*页`)
		matches3 := re3.FindStringSubmatch(text)
		if len(matches3) >= 2 {
			if pageNum, err := strconv.Atoi(matches3[1]); err == nil {
				if pageNum > maxPage {
					maxPage = pageNum
					fmt.Printf("📄 从页数格式中找到最大页码: %s -> %d\n", text, pageNum)
				}
			}
		}
	}

	// 输出调试信息
	fmt.Printf("🔍 总共检查了 %d 段文本\n", len(pageTexts))
	if maxPage == 1 {
		fmt.Printf("⚠️ 未能从页面中解析到大于1的页码，尝试查看页面内容...\n")
		// 输出一些页面文本用于调试
		for i, text := range pageTexts {
			if i >= 10 { // 只输出前10段
				break
			}
			if len(text) > 5 && len(text) < 100 {
				fmt.Printf("📝 文本片段 %d: %s\n", i+1, text)
			}
		}
	}

	fmt.Printf("📄 检测到最大页码: %d\n", maxPage)
	return maxPage
}

// getLatestPostFromDB 从数据库获取最新的帖子记录
func getLatestPostFromDB() *entity.Post {
	fmt.Printf("🔍 检查数据库中的最新记录...\n")

	post, err := dal.Post.Order(dal.Post.CreatedAt.Desc()).First()
	if err != nil {
		fmt.Printf("📝 数据库中暂无记录，将从最新页面开始爬取\n")
		return nil
	}

	fmt.Printf("📌 找到最新记录: %s (PostID: %s)\n", post.Title, post.PostID)
	return post
}

// findStartPageByPostID 根据帖子ID查找应该从哪一页开始爬取
func findStartPageByPostID(postID string) int {
	fmt.Printf("🔍 查找帖子ID %s 在哪一页...\n", postID)

	maxPage := getMaxPageNumber()

	// 从最新页面开始查找，直到找到这个帖子ID
	for page := maxPage; page >= 1; page-- {
		postUrl := fmt.Sprintf("%s/forum-40-%d.html", domain, page)

		resp, err := http.R().Get(postUrl)
		if err != nil {
			fmt.Printf("⚠️ 访问第%d页失败: %v\n", page, err)
			continue
		}

		doc, err := goquery.NewDocumentFromReader(resp.Body)
		if err != nil {
			fmt.Printf("⚠️ 解析第%d页HTML失败: %v\n", page, err)
			continue
		}

		// 查找是否包含目标帖子ID
		found := false
		doc.Find(".group .t a").Each(func(_ int, selection *goquery.Selection) {
			href, exists := selection.Attr("href")
			if !exists || !strings.Contains(href, "thread-") {
				return
			}

			re := regexp.MustCompile(`thread-(\d+)-\d+-\d+\.html`)
			matches := re.FindStringSubmatch(href)
			if len(matches) >= 2 && matches[1] == postID {
				found = true
				return
			}
		})

		if found {
			// 找到了目标帖子，从它的前一页开始爬取（避免重复处理）
			nextPage := page - 1
			if nextPage < 1 {
				nextPage = 1
			}
			fmt.Printf("📍 找到帖子ID %s 在第%d页，将从第%d页开始爬取\n", postID, page, nextPage)
			return nextPage
		}

		// 避免请求过频
		time.Sleep(1 * time.Second)
	}

	fmt.Printf("⚠️ 未找到帖子ID %s，将从最新页面开始爬取\n", postID)
	return maxPage
}

func handlePage(page int) {
	postUrl := fmt.Sprintf("%s/forum-40-%d.html", domain, page)
	fmt.Printf("正在访问页面: %s\n", postUrl)

	resp, err := req.R().Get(postUrl)
	if err != nil {
		fmt.Printf("访问页面失败: %v\n", err)
		return
	}

	fmt.Printf("HTTP状态码: %d\n", resp.StatusCode)

	doc, err := goquery.NewDocumentFromReader(resp.Body)
	if err != nil {
		fmt.Printf("解析HTML失败: %v\n", err)
		return
	}

	var postIdList [][4]string

	// 查找蕾丝猫网站的帖子链接
	doc.Find(".group .t a").Each(func(_ int, selection *goquery.Selection) {
		href, exists := selection.Attr("href")
		if !exists || !strings.Contains(href, "thread-") {
			return
		}

		title := selection.Text()

		// 从href中提取thread ID，例如: thread-38099-1-1.html -> 38099
		re := regexp.MustCompile(`thread-(\d+)-\d+-\d+\.html`)
		matches := re.FindStringSubmatch(href)
		if len(matches) < 2 {
			return // 跳过无法提取ID的链接
		}
		postId := matches[1]

		// 检查是否是有效的帖子标题
		if title != "" && !strings.Contains(title, "页") && !strings.Contains(title, "Page") {
			// 强制构造第1页的URL，确保总是从第1页开始处理
			fullUrl := fmt.Sprintf("%s/thread-%s-1-1.html", domain, postId)
			fmt.Printf("🔗 原始链接: %s, 修正为第1页: %s\n", href, fmt.Sprintf("thread-%s-1-1.html", postId))

			// 提取缩略图作为封面
			var thumbnailUrl string
			groupDiv := selection.Closest(".group")
			if groupDiv.Length() > 0 {
				thumbnailImg := groupDiv.Find(".photo img").First()
				if thumbnailImg.Length() > 0 {
					if src, exists := thumbnailImg.Attr("src"); exists {
						thumbnailUrl = src
					}
				}
			}

			postIdList = append([][4]string{{title, postId, fullUrl, thumbnailUrl}}, postIdList...)
		}
	})

	fmt.Printf("找到 %d 个帖子\n", len(postIdList))

	if len(postIdList) == 0 {
		fmt.Println("没有找到任何帖子")
	}

	//var wg sync.WaitGroup
	for _, post := range postIdList {
		title, postId, fullUrl, thumbnailUrl := post[0], post[1], post[2], post[3]

		// 检查数据库中是否已存在
		existingPost, err := dal.Post.Where(dal.Post.PostID.Eq(postId)).First()
		if err == nil && existingPost != nil {
			fmt.Printf("帖子 %s 已存在，跳过处理\n", postId)
			continue
		}

		fmt.Printf("处理帖子: %s\n", title)
		handlePost(title, postId, fullUrl, thumbnailUrl) // 传递缩略图URL
		time.Sleep(10 * time.Second)
	}
	//wg.Wait()
}

// loginToWebsite 登录网站
func loginToWebsite() error {
	if username == "" || password == "" {
		return fmt.Errorf("用户名或密码为空")
	}

	fmt.Printf("🔐 正在登录网站...\n")

	// 第一步：访问登录页面获取必要的参数
	loginPageURL := domain + "/member.php?mod=logging&action=login"
	resp, err := http.R().
		SetHeader("Accept", "text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8").
		SetHeader("Accept-Language", "zh-CN,zh;q=0.9,en;q=0.8").
		SetHeader("Cache-Control", "no-cache").
		SetHeader("Pragma", "no-cache").
		SetHeader("Upgrade-Insecure-Requests", "1").
		Get(loginPageURL)

	if err != nil {
		return fmt.Errorf("访问登录页面失败: %v", err)
	}

	fmt.Printf("📄 登录页面状态码: %d\n", resp.StatusCode)

	// 从HTML中提取formhash和loginhash
	doc, err := goquery.NewDocumentFromReader(resp.Body)
	if err != nil {
		return fmt.Errorf("解析登录页面HTML失败: %v", err)
	}

	formhash := doc.Find("input[name='formhash']").AttrOr("value", "")
	if formhash == "" {
		return fmt.Errorf("未找到formhash")
	}

	// 尝试从action URL中提取loginhash
	loginForm := doc.Find("form[name='login']")
	actionURL := loginForm.AttrOr("action", "")
	fmt.Printf("📝 表单action: %s\n", actionURL)

	// 从action URL中提取loginhash
	loginhash := ""
	if strings.Contains(actionURL, "loginhash=") {
		parts := strings.Split(actionURL, "loginhash=")
		if len(parts) > 1 {
			loginhash = parts[1]
		}
	}

	fmt.Printf("📝 获取到formhash: %s\n", formhash)
	fmt.Printf("📝 获取到loginhash: %s\n", loginhash)

	// 构造完整的登录URL
	loginURL := domain + "/member.php?mod=logging&action=login&loginsubmit=yes&handlekey=login"
	if loginhash != "" {
		loginURL += "&loginhash=" + loginhash
	}

	// 构造登录数据
	loginData := map[string]string{
		"formhash":    formhash,
		"referer":     domain + "/",
		"username":    username,
		"password":    password,
		"questionid":  "0",       // 不使用安全提问
		"cookietime":  "2592000", // 自动登录30天
		"loginsubmit": "yes",
	}

	fmt.Printf("🚀 发送登录请求到: %s\n", loginURL)

	// 发送登录请求
	loginResp, err := http.R().
		SetHeader("Accept", "application/json, text/javascript, */*; q=0.01").
		SetHeader("Accept-Language", "zh-CN,zh;q=0.9,en;q=0.8").
		SetHeader("Content-Type", "application/x-www-form-urlencoded; charset=UTF-8").
		SetHeader("Origin", domain).
		SetHeader("Referer", loginPageURL).
		SetHeader("X-Requested-With", "XMLHttpRequest").
		SetFormData(loginData).
		Post(loginURL)

	if err != nil {
		return fmt.Errorf("登录请求失败: %v", err)
	}

	fmt.Printf("📊 登录响应状态码: %d\n", loginResp.StatusCode)
	fmt.Printf("📊 登录响应内容: %s\n", loginResp.String())

	// 检查登录结果
	if loginResp.StatusCode != 200 {
		return fmt.Errorf("登录失败，HTTP状态码: %d", loginResp.StatusCode)
	}

	// 等待一下让cookie生效
	time.Sleep(2 * time.Second)

	// 验证登录是否成功（重新访问主页检查是否有用户信息）
	fmt.Printf("🔍 验证登录状态...\n")
	testResp, err := http.R().
		SetHeader("Accept", "text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8").
		Get(domain + "/")

	if err != nil {
		return fmt.Errorf("验证登录状态失败: %v", err)
	}

	testDoc, _ := goquery.NewDocumentFromReader(testResp.Body)

	// 检查是否有用户信息（比如查找用户名或者"退出"链接）
	userInfo := testDoc.Find("#user").Text()
	logoutLinks := testDoc.Find("a[href*='logout']").Length()
	loginLinks := testDoc.Find("a[href*='logging'][href*='login']").Length()

	fmt.Printf("👤 用户区域内容: %s\n", strings.TrimSpace(userInfo))
	fmt.Printf("🔗 登录链接数量: %d\n", loginLinks)
	fmt.Printf("🔗 退出链接数量: %d\n", logoutLinks)

	if logoutLinks > 0 || (loginLinks == 0 && strings.Contains(userInfo, username)) {
		fmt.Printf("✅ 登录成功！\n")
		return nil
	} else {
		return fmt.Errorf("登录验证失败，可能用户名或密码错误")
	}
}

func main() {
	fmt.Println("🚀 程序启动中...")

	fmt.Println("📦 初始化数据库连接...")
	dal.NewGormDB()
	fmt.Println("✅ 数据库连接成功")

	fmt.Println("🤖 初始化Telegram Bot...")
	var err error
	bot, err = telego.NewBot(botToken)
	if err != nil {
		fmt.Printf("❌ Telegram Bot初始化失败: %v\n", err)
		return
	}
	fmt.Println("✅ Telegram Bot初始化成功")

	// 尝试登录网站
	if err := loginToWebsite(); err != nil {
		fmt.Printf("❌ 网站登录失败: %v\n", err)
		fmt.Println("❌ 登录是必需的，程序退出")
		return
	}

	if useTelegraphPost {
		fmt.Println("📰 初始化Telegraph客户端...")
		err = initTelegraph()
		if err != nil {
			fmt.Printf("❌ Telegraph初始化失败: %v\n", err)
			return
		}
	}

	fmt.Println("🔄 开始智能抓取...")

	for {
		// 1. 获取最大页码
		maxPage := getMaxPageNumber()
		if maxPage <= 0 {
			fmt.Printf("❌ 无法获取最大页码，等待5分钟后重试...\n")
			time.Sleep(5 * time.Minute)
			continue
		}

		// 2. 检查数据库中的最新记录
		latestPost := getLatestPostFromDB()
		startPage := maxPage // 默认从最新页开始

		if latestPost != nil {
			// 3. 根据最新记录确定开始页码
			startPage = findStartPageByPostID(latestPost.PostID)
			fmt.Printf("🔄 断点续爬：从第%d页开始（数据库最新记录：%s）\n", startPage, latestPost.PostID)
		} else {
			fmt.Printf("🆕 首次运行：从最大页码第%d页开始\n", startPage)
		}

		fmt.Printf("🚀 开始从第 %d 页爬取到第 1 页（共 %d 页）\n", startPage, maxPage)

		// 4. 从后往前爬取
		for page := startPage; page >= 1; page-- {
			println("=====================", page, "=====================")
			handlePage(page)

			if page > 1 { // 不是最后一页时才等待
				fmt.Printf("⏰ 等待60秒后处理下一页...\n")
				time.Sleep(60 * time.Second)
			}
		}

		fmt.Printf("💤 本轮抓取完成，等待60分钟后开始下一轮...\n")
		time.Sleep(60 * time.Minute)
	}
}

// findCorrespondingMessage 尝试找到群组中对应的消息ID
func findCorrespondingMessage(channelMessageID int) int {
	// 获取最新消息ID来推算频道消息在群组中的ID
	latestID := getLatestMessageID()
	if latestID <= 0 {
		return 0
	}

	// 尝试几个可能的消息ID
	possibleIDs := []int{
		channelMessageID, // 直接尝试频道ID
		latestID,         // 最新消息
		latestID - 1,     // 上一条消息
		latestID - 2,     // 上上条消息
		latestID - 3,     // 再上一条
	}

	for _, messageID := range possibleIDs {
		if messageID <= 0 {
			continue
		}

		// 尝试回复这个消息ID（发送后立即删除）
		msg, err := bot.SendMessage(
			tu.Message(
				tu.ID(discussionID),
				"📸",
			).WithReplyToMessageID(messageID),
		)

		if err == nil {
			fmt.Printf("✅ 找到可回复的消息ID: %d\n", messageID)
			// 立即删除测试消息
			bot.DeleteMessage(&telego.DeleteMessageParams{
				ChatID:    tu.ID(discussionID),
				MessageID: msg.MessageID,
			})
			// 等待1秒避免频率限制
			time.Sleep(1 * time.Second)
			return messageID
		}
	}

	return 0
}

// getLatestMessageID 获取群组中最新的消息ID
func getLatestMessageID() int {
	msg, err := bot.SendMessage(
		tu.Message(
			tu.ID(discussionID),
			"📸",
		),
	)

	if err != nil {
		fmt.Printf("❌ 获取最新消息ID失败: %v\n", err)
		return 0
	}

	// 立即删除这个测试消息
	bot.DeleteMessage(&telego.DeleteMessageParams{
		ChatID:    tu.ID(discussionID),
		MessageID: msg.MessageID,
	})

	// 等待1秒避免频率限制
	time.Sleep(1 * time.Second)

	// 返回前一条消息的ID
	return msg.MessageID - 1
}

// extractImageLinks 从内容中提取所有图片链接
func extractImageLinks(content []telegraph.Node) []string {
	var imageLinks []string

	for _, node := range content {
		extractImagesFromNode(node, &imageLinks)
	}

	return imageLinks
}

// extractImagesFromNode 递归提取节点中的图片链接
func extractImagesFromNode(node telegraph.Node, imageLinks *[]string) {
	switch v := node.(type) {
	case telegraph.NodeElement:
		if v.Tag == "img" {
			if src, exists := v.Attrs["src"]; exists {
				*imageLinks = append(*imageLinks, src)
			}
		}

		// 递归处理子节点
		for _, child := range v.Children {
			extractImagesFromNode(child, imageLinks)
		}
	}
}

// sendImagesToDiscussion 在讨论群组中发送图片（使用MediaGroup批量发送）
func sendImagesToDiscussion(imageLinks []string, channelMessageID int) {
	if len(imageLinks) == 0 {
		return
	}

	// 按最大10张图片一组进行分组发送
	totalImages := len(imageLinks)
	const maxImagesPerGroup = 10
	for i := 0; i < len(imageLinks); i += maxImagesPerGroup {
		end := i + maxImagesPerGroup
		if end > len(imageLinks) {
			end = len(imageLinks)
		}

		batch := imageLinks[i:end]
		batchSize := len(batch)

		if batchSize == 1 {
			// 单张图片直接发送
			fmt.Printf("发送单张图片 %d/%d\n", i+1, totalImages)
			sendSingleImageToDiscussion(batch[0], i+1, totalImages, channelMessageID)
			continue
		}

		// 创建MediaGroup
		var mediaGroup []telego.InputMedia
		for j, imageURL := range batch {
			caption := ""
			if j == 0 {
				if batchSize == len(imageLinks) {
					caption = fmt.Sprintf("🖼️ 全部 %d 张图片", totalImages)
				} else {
					caption = fmt.Sprintf("🖼️ 第 %d-%d 张（共%d张）", i+1, i+batchSize, totalImages)
				}
			}

			mediaGroup = append(mediaGroup, tu.MediaPhoto(
				tu.FileFromURL(imageURL),
			).WithCaption(caption))
		}

		// 发送MediaGroup
		fmt.Printf("发送图片组 %d-%d/%d (%d张图片)...\n", i+1, i+batchSize, totalImages, batchSize)

		var sent bool
		var err error
		for retry := 0; retry < 5; retry++ {
			var mediaGroupBuilder *telego.SendMediaGroupParams
			if channelMessageID > 0 {
				mediaGroupBuilder = tu.MediaGroup(
					tu.ID(discussionID),
					mediaGroup...,
				).WithReplyToMessageID(channelMessageID)
			} else {
				mediaGroupBuilder = tu.MediaGroup(
					tu.ID(discussionID),
					mediaGroup...,
				)
			}
			_, err = bot.SendMediaGroup(mediaGroupBuilder)
			if err == nil {
				sent = true
				fmt.Printf("✅ 成功发送图片组 %d-%d/%d\n", i+1, i+batchSize, totalImages)
				break
			}

			// 处理频率限制错误
			if strings.Contains(err.Error(), "Too Many Requests: retry after") {
				retryAfter := 0
				fmt.Sscanf(err.Error(), "Too Many Requests: retry after %d", &retryAfter)
				if retryAfter > 0 {
					fmt.Printf("⏳ 遇到频率限制，等待 %d 秒后重试...\n", retryAfter)
					time.Sleep(time.Duration(retryAfter+1) * time.Second)
					continue
				}
			} else if strings.Contains(err.Error(), "WEBPAGE_MEDIA_EMPTY") ||
				strings.Contains(err.Error(), "wrong type of the web page content") ||
				strings.Contains(err.Error(), "WEBPAGE_CURL_FAILED") {
				fmt.Printf("❌ 图片组中有无效图片，检测并剔除无效图片后重新发送...\n")
				// 检测无效图片并重新发送
				sent = handleInvalidImages(batch, i, batchSize, totalImages, channelMessageID)
				break
			}

			fmt.Printf("发送图片组失败 (重试 %d/5): %v\n", retry+1, err.Error())
			waitTime := 5 * time.Second
			if retry < 4 {
				fmt.Printf("⏳ 等待 %d 秒后重试...\n", int(waitTime.Seconds()))
				time.Sleep(waitTime)
			}
		}

		if !sent {
			fmt.Printf("❌ 图片组发送失败，跳过该组\n")
		}

		// 组之间等待更长时间
		waitTime := 10 * time.Second
		fmt.Printf("⏳ 等待 %d 秒后处理下一组图片...\n", int(waitTime.Seconds()))
		time.Sleep(waitTime)
	}

	fmt.Printf("✅ 完成发送所有图片到讨论群组\n")
}

// handleInvalidImages 检测无效图片，剔除后重新发送
func handleInvalidImages(batch []string, batchIndex, batchSize, totalImages int, channelMessageID int) bool {
	return handleInvalidImagesWithDepth(batch, batchIndex, batchSize, totalImages, channelMessageID, 0)
}

// handleInvalidImagesWithDepth 带递归深度限制的无效图片处理
func handleInvalidImagesWithDepth(batch []string, batchIndex, batchSize, totalImages int, channelMessageID int, depth int) bool {
	if depth >= 10 { // 增加到10层，给更多机会
		fmt.Printf("❌ 递归检测深度超限（%d层），跳过该批次\n", depth)
		return false
	}
	if len(batch) == 0 {
		fmt.Printf("❌ 批次中没有图片，跳过\n")
		return true
	}
	fmt.Printf("🔍 检测批次中的无效图片...\n")

	var validImages []string
	var invalidImages []string

	// 逐个检测图片有效性
	for _, imageURL := range batch {
		isValid := checkImageValidity(imageURL)
		if isValid {
			validImages = append(validImages, imageURL)
		} else {
			invalidImages = append(invalidImages, imageURL)
		}
	}

	// 打印无效图片链接
	if len(invalidImages) > 0 {
		fmt.Printf("🚫 发现 %d 张无效图片：\n", len(invalidImages))
		for i, invalidURL := range invalidImages {
			fmt.Printf("   %d. %s\n", i+1, invalidURL)
		}
	}

	// 如果有有效图片，重新发送
	if len(validImages) > 0 {
		fmt.Printf("✅ 剔除无效图片后，重新发送 %d 张有效图片\n", len(validImages))

		// 创建新的MediaGroup
		var mediaGroup []telego.InputMedia
		for j, imageURL := range validImages {
			caption := ""
			if j == 0 {
				caption = fmt.Sprintf("🖼️ 第 %d-%d 张（共%d张，已剔除%d张无效）",
					batchIndex+1, batchIndex+len(validImages), totalImages, len(invalidImages))
			}

			mediaGroup = append(mediaGroup, tu.MediaPhoto(
				tu.FileFromURL(imageURL),
			).WithCaption(caption))
		}

		// 重新发送有效图片
		var err error
		for retry := 0; retry < 3; retry++ {
			var mediaGroupBuilder *telego.SendMediaGroupParams
			if channelMessageID > 0 {
				mediaGroupBuilder = tu.MediaGroup(
					tu.ID(discussionID),
					mediaGroup...,
				).WithReplyToMessageID(channelMessageID)
			} else {
				mediaGroupBuilder = tu.MediaGroup(
					tu.ID(discussionID),
					mediaGroup...,
				)
			}
			_, err = bot.SendMediaGroup(mediaGroupBuilder)
			if err == nil {
				fmt.Printf("✅ 成功重新发送图片组 %d-%d/%d\n", batchIndex+1, batchIndex+len(validImages), totalImages)
				return true
			}

			// 处理频率限制
			if strings.Contains(err.Error(), "Too Many Requests: retry after") {
				retryAfter := 0
				fmt.Sscanf(err.Error(), "Too Many Requests: retry after %d", &retryAfter)
				if retryAfter > 0 {
					fmt.Printf("⏳ 遇到频率限制，等待 %d 秒后重试...\n", retryAfter)
					time.Sleep(time.Duration(retryAfter+1) * time.Second)
					continue
				}
			}

			// 如果还是遇到图片问题，递归处理
			if strings.Contains(err.Error(), "WEBPAGE_MEDIA_EMPTY") ||
				strings.Contains(err.Error(), "wrong type of the web page content") ||
				strings.Contains(err.Error(), "WEBPAGE_CURL_FAILED") {
				fmt.Printf("⚠️ 重新发送时仍有无效图片，进行第二轮检测...\n")
				return handleInvalidImagesWithDepth(validImages, batchIndex, len(validImages), totalImages, channelMessageID, depth+1)
			}

			fmt.Printf("重新发送失败 (重试 %d/3): %v\n", retry+1, err.Error())
			if retry < 2 {
				time.Sleep(3 * time.Second)
			}
		}

		fmt.Printf("❌ 重新发送失败: %v\n", err.Error())
		return false
	} else {
		fmt.Printf("❌ 该批次中没有有效图片，跳过\n")
		return true // 标记为已处理（虽然没有发送）
	}
}

// checkImageValidity 检查单张图片是否有效
func checkImageValidity(imageURL string) bool {
	// 使用HTTP HEAD请求检查图片
	resp, err := http.R().Head(imageURL)
	if err != nil {
		fmt.Printf("🔍 检测图片失败 (网络错误): %s\n", imageURL)
		return false
	}

	// 检查HTTP状态码
	if resp.StatusCode != 200 {
		fmt.Printf("🔍 检测图片失败 (状态码 %d): %s\n", resp.StatusCode, imageURL)
		return false
	}

	// 检查Content-Type
	contentType := resp.Header.Get("Content-Type")
	if contentType == "" {
		fmt.Printf("🔍 检测图片失败 (无Content-Type): %s\n", imageURL)
		return false
	}

	// 检查是否为图片类型
	if !strings.HasPrefix(contentType, "image/") {
		fmt.Printf("🔍 检测图片失败 (非图片类型 %s): %s\n", contentType, imageURL)
		return false
	}

	// 检查Content-Length
	contentLength := resp.Header.Get("Content-Length")
	if contentLength != "" {
		size := cast.ToInt64(contentLength)
		if size == 0 {
			fmt.Printf("🔍 检测图片失败 (文件大小为0): %s\n", imageURL)
			return false
		}
		if size < 100 { // 小于100字节可能是错误页面
			fmt.Printf("🔍 检测图片失败 (文件太小 %d字节): %s\n", size, imageURL)
			return false
		}
	}

	return true
}

// sendSingleImageToDiscussion 发送单张图片到讨论群组
func sendSingleImageToDiscussion(imageURL string, current, total int, channelMessageID int) error {
	caption := fmt.Sprintf("🖼️ %d/%d", current, total)

	var err error
	for retry := 0; retry < 3; retry++ {
		_, err = bot.SendPhoto(
			tu.Photo(
				tu.ID(discussionID),
				tu.FileFromURL(imageURL),
			).WithCaption(caption).WithReplyToMessageID(channelMessageID),
		)
		if err == nil {
			fmt.Printf("✅ 成功发送图片 %d/%d\n", current, total)
			return nil
		}

		// 处理频率限制错误
		if strings.Contains(err.Error(), "Too Many Requests: retry after") {
			return err // 让上层处理重试
		}

		fmt.Printf("发送图片失败 (重试 %d/3): %v\n", retry+1, err.Error())
		if retry < 2 {
			time.Sleep(2 * time.Second)
		}
	}

	fmt.Printf("❌ 发送图片最终失败: %v\n", err.Error())
	return err
}
