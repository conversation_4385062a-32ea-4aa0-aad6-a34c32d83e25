// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package entity

import (
	"time"

	"gorm.io/gorm"
)

const TableNamePost = "post"

// Post mapped from table <post>
type Post struct {
	ID        int32          `gorm:"column:id;type:integer;primaryKey;autoIncrement:true" json:"id"`
	Title     string         `gorm:"column:title;type:text;not null" json:"title"`
	CreatedAt time.Time      `gorm:"column:created_at;type:timestamp without time zone;not null" json:"created_at"`
	PostID    string         `gorm:"column:post_id;type:text;not null;uniqueIndex:post_id,priority:1;uniqueIndex:post_post_id_key,priority:1;uniqueIndex:idx_post_post_id,priority:1" json:"post_id"`
	Author    string         `gorm:"column:author;type:text;not null" json:"author"`
	MessageID int32          `gorm:"column:message_id;type:integer;not null" json:"message_id"`
	Content   string         `gorm:"column:content;type:text;not null" json:"content"`
	URL       string         `gorm:"column:url;type:text;not null;default:1" json:"url"`
	TgURL     string         `gorm:"column:tg_url;type:text;not null;default:0" json:"tg_url"`
	UpdatedAt time.Time      `gorm:"column:updated_at;type:timestamp(0) without time zone;not null" json:"updated_at"`
	DeletedAt gorm.DeletedAt `gorm:"column:deleted_at;type:timestamp(0) without time zone" json:"deleted_at"`
}

// TableName Post's table name
func (*Post) TableName() string {
	return TableNamePost
}
