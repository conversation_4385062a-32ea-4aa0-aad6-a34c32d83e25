# Telegram频道评论配置说明

## ❌ 常见错误: "chat not found"

如果您看到这个错误，说明讨论群组ID配置有问题。

## 🔧 配置步骤

### 1. 获取讨论群组ID

**方法1：使用Bot**
1. 将 @userinfobot 添加到您的群组
2. 在群组中发送任何消息
3. Bot会回复群组信息，包含群组ID

**方法2：使用代码**
1. 将您的Bot添加到群组
2. 在群组中发送 `/start`
3. 查看Bot日志获取群组ID

### 2. 确保Bot权限

**重要：Bot必须在群组中有以下权限：**
- ✅ 发送消息
- ✅ 发送媒体
- ✅ 删除消息（可选）
- ✅ 设为管理员（推荐）

### 3. 将群组关联到频道

1. 进入频道设置 → 讨论
2. 选择您的群组作为讨论群组
3. 保存设置

### 4. 修改代码配置

在 `main.go` 文件第24行，修改 `discussionID`：

```go
discussionID = int64(-1001234567890)  // 改成您的实际讨论群组ID
```

## 程序工作原理

1. **发送消息到频道** → 自动同步到讨论群组
2. **提取图片链接** → 逐个发送到讨论群组作为回复
3. **群组回复** → 自动显示为频道评论

## 预期效果

**频道中：**
```
📰 帖子标题
👤 作者：#作者名
🏷️ 关键词：关键词
🔗 原文：链接

💬 评论 (21)  ← 点击查看图片
```

**评论区中：**
```
📸 共 21 张图片：
🖼️ 1/21
🖼️ 2/21
...
🖼️ 21/21
```

## 🧪 测试配置

### 正常输出（配置正确）

```
✅ 成功发送消息到频道，消息ID: 75
找到 21 张图片
在讨论群组中发送图片...
🔍 调试信息：
   - 频道ID: -1002369927444
   - 讨论群组ID: -1001234567890
   - 频道消息ID: 75
🧪 测试步骤1：能否发送消息到讨论群组...
✅ 测试步骤1通过：Bot可以发送消息到讨论群组
🧪 测试步骤2：查找群组中对应的消息...
⏰ 等待3秒让频道消息同步到群组...
🔍 当前群组消息ID: 102，频道消息ID: 75
🧪 测试回复消息ID: 75
❌ 无法回复消息ID 75
🧪 测试回复消息ID: 101
✅ 成功回复消息ID: 101
✅ 找到群组中对应的消息ID: 101
🚀 开始发送图片并回复消息...
📸 共 21 张图片：
✅ 成功发送图片 1/21
✅ 成功发送图片 2/21
...
✅ 完成发送 21 张图片到讨论群组
```

### 错误输出1：群组配置错误

```
❌ 测试步骤1失败: telego: sendMessage(): api: 400 "Bad Request: chat not found"
❌ 测试步骤1最终失败！
🔧 可能的解决方案：
   1. 检查discussionID是否正确 (当前: -1001234567890)
   2. 确保Bot已添加到讨论群组并设为管理员
   3. 使用 @userinfobot 获取正确的群组ID
```

**解决方案：**
- 检查 `discussionID` 是否正确
- 确保Bot在群组中有权限

### 错误输出2：无法找到对应消息

```
✅ 测试步骤1通过：Bot可以发送消息到讨论群组
🧪 测试步骤2：查找群组中对应的消息...
⏰ 等待3秒让频道消息同步到群组...
🔍 当前群组消息ID: 102，频道消息ID: 75
🧪 测试回复消息ID: 75
❌ 无法回复消息ID 75
🧪 测试回复消息ID: 101
❌ 无法回复消息ID 101
🧪 测试回复消息ID: 100
❌ 无法回复消息ID 100
🧪 测试回复消息ID: 99
❌ 无法回复消息ID 99
❌ 无法找到群组中对应的消息
🔧 可能的原因：
   1. 频道消息没有自动转发到讨论群组
   2. 群组没有正确关联到频道
   3. 需要检查频道设置中的讨论功能
💡 尝试方案：直接回复最新消息
📝 找到群组最新消息ID: 101，尝试回复...
📸 共 21 张图片：
✅ 成功发送图片 1/21
...
```

**解决方案：**
- 在频道设置中正确关联讨论群组
- 确保频道消息会自动转发到群组
- 程序会自动尝试多种方案确保图片能正确关联

## 🚀 快速修复

1. **获取正确的群组ID：**
   ```
   将 @userinfobot 添加到群组 → 发送消息 → 获取ID
   ```

2. **修改配置：**
   ```go
   discussionID = int64(-1001234567890)  // 替换为真实ID
   ```

3. **重新运行程序**