package main

import (
	"bytes"
	"fmt"
	"io"
	"log"
	"regexp"
	"strconv"
	"strings"
	"time"

	"github.com/PuerkitoBio/goquery"
	"github.com/imroc/req/v3"
	"github.com/meinside/telegraph-go"
	"golang.org/x/net/html"
)

var (
	uploadImg     = false // 临时禁用图片上传以加快处理速度
	availableTags = []string{
		"img", "a", "ul", "li", "ol", "video",
		// "a", "aside", "b", "blockquote", "code", "em", "figcaption", "figure", "h3", "h4", "hr", "i", "iframe", "img", "li", "ol", "p", "pre", "s", "strong", "u", "ul", "video",
	}
	availableAttrs = []string{
		"href", "ess-data",
	}

	// Telegraph客户端和令牌
	telegraphClient *telegraph.Client
	telegraphToken  = "cfb3b610f311684497f74f45443868b8f1979d1fe316e95f5c29cecd22ea"
)

type Imgs []struct {
	Src string `json:"src"`
}

func contains(s []string, e string) bool {
	for _, a := range s {
		if a == e {
			return true
		}
	}
	return false
}

func purify(selections *goquery.Selection) []telegraph.Node {
	var nodes = []telegraph.Node{}

	var tag string
	var attrs map[string]string
	sizeRe := regexp.MustCompile(`^ ?\(\d+(\.\d+)? (KB|MB)\)$`)
	replacer := strings.NewReplacer("\n", "", "\t", "")
	//dateRe := regexp.MustCompile(`^\d+.天前.\d{1,2}:\d{1,2}$`)
	//edit := regexp.MustCompile(`[\s\S]*本帖最后由[\s\S]*于[\s\S]*编辑[\s\S]*$`)
	edit := regexp.MustCompile(`[\s\S]*此貼被[\s\S]*在[\s\S]*重新編輯[\s\S]*$`)
	//downloadTimes := regexp.MustCompile(`下载次数:\d+`)
	var element telegraph.NodeElement

	selections.Each(func(_ int, child *goquery.Selection) {
		for _, node := range child.Nodes {
			switch node.Type {
			case html.TextNode:
				str := node.Data
				str = edit.ReplaceAllString(str, "")
				//str = dateRe.ReplaceAllString(str, "")
				str = sizeRe.ReplaceAllString(str, "")
				//str = downloadTimes.ReplaceAllString(str, "")
				str = replacer.Replace(str)

				if str == "" {
					continue
				}
				element = telegraph.NodeElement{
					Tag:      "p",
					Children: []telegraph.Node{str},
				}
				nodes = append(nodes, element) // append text
			case html.ElementNode:
				attrs = map[string]string{}
				attachInfoEle := false
				for _, attr := range node.Attr {
					if attr.Key == "class" && (attr.Val == "t_attach" || attr.Val == "attach_popup") {
						attachInfoEle = true
						break
					}
					if attr.Key == "class" && (attr.Val == "t_like" || attr.Val == "tips") {
						attachInfoEle = true
						break
					}
					if !contains(availableAttrs, attr.Key) {
						continue
					}
					attrs[attr.Key] = attr.Val
				}
				if attachInfoEle {
					continue
				}

				// new node element
				if len(node.Namespace) > 0 {
					tag = fmt.Sprintf("%s.%s", node.Namespace, node.Data)
				} else {
					tag = node.Data
				}
				if contains(availableTags, tag) {
					if (tag == "img" && !strings.HasPrefix(attrs["ess-data"], "http")) ||
						(tag == "a" && !strings.HasPrefix(attrs["href"], "http")) {
						continue
					}

					if tag == "img" {

						if uploadImg {
							w := new(bytes.Buffer)
							_, err := req.R().
								SetHeader("user-agent", "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/113.0.1774.57").
								SetOutput(w).
								SetRetryCount(8).Get(attrs["ess-data"])
							if err != nil {
								log.Printf("download %s error: %s", attrs["href"], err.Error())
								continue
							}
							imgs := Imgs{}
							resp, err := req.R().
								SetRetryCount(8).
								SetSuccessResult(&imgs).
								SetRetryCondition(func(resp *req.Response, err error) bool {
									return err != nil || resp.StatusCode >= 500
								}).
								SetFileUpload(req.FileUpload{
									ParamName:   "file",
									FileName:    "blob",
									ContentType: "image/jpg",
									GetFileContent: func() (io.ReadCloser, error) {
										return io.NopCloser(bytes.NewReader(w.Bytes())), nil
									},
								}).
								Post("https://telegra.ph/upload")
							if err != nil {
								log.Printf("上传图片失败 %s: %s", attrs["ess-data"], err.Error())
								// 使用原图片URL作为fallback
								attrs["src"] = attrs["ess-data"]
								delete(attrs, "ess-data")
							} else if len(imgs) > 0 {
								delete(attrs, "ess-data")
								attrs["src"] = imgs[0].Src
								log.Printf("图片上传成功: %s -> %s", attrs["ess-data"], imgs[0].Src)
							} else {
								log.Printf("图片上传响应为空: %s, 响应: %s", attrs["ess-data"], resp.String())
								// 使用原图片URL作为fallback
								attrs["src"] = attrs["ess-data"]
								delete(attrs, "ess-data")
							}
						} else {
							attrs["src"] = attrs["ess-data"]
							delete(attrs, "ess-data")
						}

					}
					element = telegraph.NodeElement{
						Tag:      tag,
						Attrs:    attrs,
						Children: purify(child.Contents()),
					}
					nodes = append(nodes, element) // append element
				} else {
					nodes = append(nodes, purify(child.Contents())...)
				}

			default:
				continue
			}
		}
	})

	return nodes
}

func PureHtml(html io.Reader) (string, []telegraph.Node, string, string, int) {
	// 将Reader转换为bytes，以便多次读取
	htmlBytes, err := io.ReadAll(html)
	if err != nil {
		return "", nil, "", "", 0
	}

	doc, _ := goquery.NewDocumentFromReader(bytes.NewReader(htmlBytes))

	// 蕾丝猫网站的结构解析
	// 获取作者信息（从标题中提取或者设置默认值）
	//title := doc.Find("#thread-title h1").Text()
	author := "" // 默认作者，也可以尝试从其他地方提取

	// 提取关键词
	var keywords string
	// 需要过滤的关键词列表
	filterWords := map[string]bool{
		"套图": true,
	}
	
	doc.Find("#thread-tag a").Each(func(i int, s *goquery.Selection) {
		keyword := strings.TrimSpace(s.Text())
		if keyword != "" && !filterWords[keyword] {
			keywords += "#" + keyword + " "
		}
	})
	keywords = strings.TrimSpace(keywords) // 去除末尾空格
	fmt.Printf("🏷️ 提取到关键词: %s\n", keywords)

	// 提取封面图片
	var coverImage string
	coverImg := doc.Find("#thread-pic ul.adw li img").First()
	if coverImg.Length() > 0 {
		if src, exists := coverImg.Attr("src"); exists {
			coverImage = src
			fmt.Printf("📷 提取到封面图片: %s\n", coverImage)
		}
	}

	// 创建图片节点
	var nodes []telegraph.Node

	// 添加标题段落
	//nodes = append(nodes, telegraph.NodeElement{
	//	Tag: "h3",
	//	Children: []telegraph.Node{
	//		fmt.Sprintf("📷 %s", doc.Find("#thread-title h1").Text()),
	//	},
	//})

	// 添加分隔线
	nodes = append(nodes, telegraph.NodeElement{
		Tag: "hr",
	})

	// 查找第一页图片
	imageCount := 0
	doc.Find("#thread-pic ul.adw li img").Each(func(i int, s *goquery.Selection) {
		src, exists := s.Attr("src")
		if exists {
			imageCount++
			// 为每张图片添加一个段落包装
			nodes = append(nodes, telegraph.NodeElement{
				Tag: "p",
				Children: []telegraph.Node{
					telegraph.NodeElement{
						Tag: "img",
						Attrs: map[string]string{
							"src": src,
						},
					},
				},
			})

			// 每5张图片添加一个分隔
			if imageCount%5 == 0 {
				nodes = append(nodes, telegraph.NodeElement{
					Tag: "br",
				})
			}
		}
	})

	// 处理分页情况
	// 查看是否有下一页链接并获取所有页面的图片
	allPageNodes := getAllPagesImages(string(htmlBytes))

	// 为分页图片也添加段落包装
	for _, pageNode := range allPageNodes {
		if imgNode, ok := pageNode.(telegraph.NodeElement); ok && imgNode.Tag == "img" {
			imageCount++
			nodes = append(nodes, telegraph.NodeElement{
				Tag:      "p",
				Children: []telegraph.Node{imgNode},
			})

			// 每5张图片添加一个分隔
			if imageCount%5 == 0 {
				nodes = append(nodes, telegraph.NodeElement{
					Tag: "br",
				})
			}
		}
	}

	// 添加结尾信息
	nodes = append(nodes, telegraph.NodeElement{
		Tag: "hr",
	})
	//nodes = append(nodes, telegraph.NodeElement{
	//	Tag: "p",
	//	Children: []telegraph.Node{
	//		fmt.Sprintf("📊 共 %d 张图片", imageCount),
	//	},
	//})

	// 如果没有找到图片，返回空
	if len(nodes) == 0 {
		return author, nil, coverImage, keywords, 0
	}

	return author, nodes, coverImage, keywords, imageCount
}

// getAllPagesImages 获取所有分页的图片
func getAllPagesImages(firstPageHTML string) []telegraph.Node {
	var allNodes []telegraph.Node

	doc, _ := goquery.NewDocumentFromReader(strings.NewReader(firstPageHTML))

	// 首先识别当前页码
	currentPage := 1

	// 方法1：从strong标签获取当前页码
	doc.Find("#thread-page .pg strong").Each(func(i int, s *goquery.Selection) {
		pageText := strings.TrimSpace(s.Text())
		if pageNum, err := strconv.Atoi(pageText); err == nil {
			currentPage = pageNum
		}
	})

	// 方法2：如果没有找到strong标签，尝试从URL中解析
	if currentPage == 1 {
		// 查找当前页面的URL信息，可能在某些元素中
		doc.Find("a").Each(func(i int, s *goquery.Selection) {
			href, exists := s.Attr("href")
			if exists && strings.Contains(href, "thread-") {
				// 从href中提取页码，例如: thread-1511-2-1.html -> 2
				re := regexp.MustCompile(`thread-\d+-(\d+)-\d+\.html`)
				matches := re.FindStringSubmatch(href)
				if len(matches) >= 2 {
					if pageNum, err := strconv.Atoi(matches[1]); err == nil && pageNum > currentPage {
						// 如果找到的页码比当前页码大，说明当前页码至少是这个数字-1
						// 但这个逻辑不够准确，我们主要还是依赖strong标签
					}
				}
			}
		})
	}

	fmt.Printf("🔍 当前页码: %d\n", currentPage)

	// 查找分页链接，只处理当前页之后的页面
	var pageLinks []string
	var pageNumbers []int

	doc.Find("#thread-page .pg a").Each(func(i int, s *goquery.Selection) {
		href, exists := s.Attr("href")
		if !exists || !strings.Contains(href, "thread-") || strings.Contains(href, "javascript") {
			return
		}

		// 从href中提取页码，例如: thread-1504-4-1.html -> 4
		re := regexp.MustCompile(`thread-\d+-(\d+)-\d+\.html`)
		matches := re.FindStringSubmatch(href)
		if len(matches) >= 2 {
			if pageNum, err := strconv.Atoi(matches[1]); err == nil {
				// 只处理当前页之后的页面
				if pageNum > currentPage {
					// 避免重复添加同一页面
					alreadyExists := false
					for _, existingPageNum := range pageNumbers {
						if existingPageNum == pageNum {
							alreadyExists = true
							break
						}
					}
					if !alreadyExists {
						pageLinks = append(pageLinks, href)
						pageNumbers = append(pageNumbers, pageNum)
					}
				}
			}
		}
	})

	if len(pageLinks) == 0 {
		fmt.Printf("ℹ️ 没有发现后续分页，可能只有 %d 页或已是最后一页\n", currentPage)
		return allNodes
	}

	fmt.Printf("🔍 发现 %d 个后续分页链接 (页码: %v)\n", len(pageLinks), pageNumbers)

	// 按页码排序，确保按顺序处理
	for i := 0; i < len(pageNumbers)-1; i++ {
		for j := i + 1; j < len(pageNumbers); j++ {
			if pageNumbers[i] > pageNumbers[j] {
				// 交换页码
				pageNumbers[i], pageNumbers[j] = pageNumbers[j], pageNumbers[i]
				// 交换对应的链接
				pageLinks[i], pageLinks[j] = pageLinks[j], pageLinks[i]
			}
		}
	}

	// 限制最多处理10页，避免过度请求
	maxPages := 10
	if len(pageLinks) > maxPages {
		fmt.Printf("⚠️ 分页数量过多，限制处理前 %d 页\n", maxPages)
		pageLinks = pageLinks[:maxPages]
		pageNumbers = pageNumbers[:maxPages]
	}

	// 获取每个分页的图片
	for i, pageHref := range pageLinks {
		actualPageNum := pageNumbers[i]
		fmt.Printf("📖 正在获取第 %d 页图片：%s\n", actualPageNum, pageHref)

		pageURL := fmt.Sprintf("https://www.lsmao.org/%s", pageHref)

		// 请求分页，增加重试机制
		var resp *req.Response
		var err error
		client := req.C().ImpersonateChrome()

		for retry := 0; retry < 3; retry++ {
			resp, err = client.R().Get(pageURL)
			if err == nil && resp.StatusCode == 200 {
				break
			}
			if retry < 2 {
				fmt.Printf("⏳ 获取分页失败，等待2秒后重试...\n")
				time.Sleep(2 * time.Second)
			}
		}

		if err != nil {
			fmt.Printf("❌ 获取分页失败（重试3次后）：%v\n", err)
			continue
		}

		if resp.StatusCode != 200 {
			fmt.Printf("❌ 分页HTTP状态码异常：%d\n", resp.StatusCode)
			continue
		}

		// 解析分页HTML
		pageDoc, err := goquery.NewDocumentFromReader(resp.Body)
		if err != nil {
			fmt.Printf("❌ 解析分页HTML失败：%v\n", err)
			continue
		}

		// 提取分页中的图片
		pageImageCount := 0
		pageDoc.Find("#thread-pic ul.adw li img").Each(func(i int, s *goquery.Selection) {
			src, exists := s.Attr("src")
			if exists && src != "" {
				allNodes = append(allNodes, telegraph.NodeElement{
					Tag: "img",
					Attrs: map[string]string{
						"src": src,
					},
				})
				pageImageCount++
			}
		})

		fmt.Printf("✅ 第 %d 页获取到 %d 张图片\n", actualPageNum, pageImageCount)

		// 分页之间等待2秒，避免请求过频
		time.Sleep(2 * time.Second)
	}

	fmt.Printf("🎉 总共从分页获取到 %d 张图片\n", len(allNodes))
	return allNodes
}

// initTelegraph 初始化Telegraph客户端
func initTelegraph() error {
	// 直接使用现有token，不需要创建账户
	// meinside/telegraph-go库的API比较简单，直接使用token即可
	telegraphClient = &telegraph.Client{AccessToken: telegraphToken}
	fmt.Printf("✅ 使用Telegraph Token: %s...\n", telegraphToken[:20])
	return nil
}

// createTelegraphPage 创建Telegraph页面
func createTelegraphPage(title, author string, content []telegraph.Node) (*telegraph.Page, error) {
	if telegraphClient == nil {
		return nil, fmt.Errorf("Telegraph客户端未初始化")
	}

	// 限制标题长度，Telegraph有长度限制
	if len(title) > 256 {
		title = title[:253] + "..."
	}

	// 重试机制
	var page telegraph.Page
	var err error

	for retry := 0; retry < 3; retry++ {
		fmt.Printf("📝 创建Telegraph页面 (尝试 %d/3): %s\n", retry+1, title)

		// 根据meinside/telegraph-go的API调用方式
		// 使用"#"作为作者链接，这样点击作者时不会跳转
		page, err = telegraphClient.CreatePage(title, author, "#", content, true)

		if err == nil {
			fmt.Printf("✅ 成功创建Telegraph页面: %s\n", page.URL)
			return &page, nil
		}

		fmt.Printf("❌ 创建Telegraph页面失败 (尝试 %d/3): %v\n", retry+1, err)

		// 如果是token问题，尝试重新初始化
		if strings.Contains(err.Error(), "ACCESS_TOKEN_INVALID") || strings.Contains(err.Error(), "unauthorized") {
			fmt.Printf("🔧 Token无效，重新初始化Telegraph...\n")
			initErr := initTelegraph()
			if initErr != nil {
				fmt.Printf("❌ 重新初始化失败: %v\n", initErr)
			}
		}

		if retry < 2 {
			time.Sleep(time.Duration(2*(retry+1)) * time.Second)
		}
	}

	return nil, fmt.Errorf("创建Telegraph页面失败（重试3次后）: %v", err)
}
