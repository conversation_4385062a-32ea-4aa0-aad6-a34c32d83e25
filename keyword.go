package main

import (
	"fmt"
	"github.com/huichen/sego"
	"github.com/imroc/req/v3"
	"github.com/tidwall/gjson"
	"strings"
)

type AIResponse struct {
	ID      string    `json:"id"`
	Object  string    `json:"object"`
	Created int       `json:"created"`
	Model   string    `json:"model"`
	Choices []Choices `json:"choices"`
	Usage   Usage     `json:"usage"`
}
type Message struct {
	Role    string `json:"role"`
	Content string `json:"content"`
}
type Choices struct {
	Index        int     `json:"index"`
	Message      Message `json:"message"`
	FinishReason string  `json:"finish_reason"`
}
type Usage struct {
	PromptTokens     int `json:"prompt_tokens"`
	CompletionTokens int `json:"completion_tokens"`
	TotalTokens      int `json:"total_tokens"`
}

type Session struct {
	RefreshToken          string `json:"refresh_token"`
	RefreshTokenExpiresIn int    `json:"refresh_token_expires_in"`
	TokenType             string `json:"token_type"`
	ExpiresIn             int    `json:"expires_in"`
	AccessToken           string `json:"access_token"`
	SetappToken           string `json:"setapp_token"`
}

func getToken() string {
	var session Session

	codeString := http.R().
		SetBodyJsonString(fmt.Sprintf(`{
		  "client_id": "2862d686bf317a8cf76675b914795a6baf0f19fb2e10998a",
		  "scope": "ai.openai",
		  "session_token": "0c0392b3-ec86-4ce1-b336-80cb4fcef91e",
		  "response_type": "code"
		}`)).MustPost("https://vendor-api.setapp.com/auth/v1/authorize").String()
	code := gjson.Get(codeString, "code").String()

	req.R().SetFormData(map[string]string{
		"grant_type": "authorization_code_by_setapp_library",
		"code":       code,
	}).
		SetHeaders(map[string]string{
			"authorization": "Basic Mjg2MmQ2ODZiZjMxN2E4Y2Y3NjY3NWI5MTQ3OTVhNmJhZjBmMTlmYjJlMTA5OThhOlBZTG1DamdyMk92R0wwU3VtT3llOTlqN3N3VnVDWmd4Umw0Q0hHcVQ=",
			"user-agent":    "Plus/131CFNetwork/1408.0.4Darwin/22.5.0",
		}).
		SetSuccessResult(&session).Post("https://vendor-api.setapp.com/auth/v1/token")

	return session.AccessToken

}

var segmenter sego.Segmenter

func init() {
	segmenter.LoadDictionary("./dictionary.txt")
}

func GetKeywords(title string, times int) string {
	if times > 3 {
		return ""
	}

	text := []byte(title)
	segments := segmenter.Segment(text)
	keywords := sego.SegmentsToSlice(segments, true)

	result := ""
	// 过滤掉无意义的词和标点符号
	skipWords := map[string]bool{
		"[": true, "]": true, "（": true, "）": true, "(": true, ")": true,
		"［": true, "］": true, "【": true, "】": true,
		"，": true, "。": true, "？": true, "！": true, "：": true, "；": true,
		"、": true, "\"": true, "'": true, "《": true, "》": true,
		"-": true, "—": true, "～": true, "~": true, "+": true, "=": true,
		"就是": true, "有": true, "没有": true, "是": true, "不是": true, "了": true,
		"的": true, "地": true, "得": true, "和": true, "与": true, "或": true,
		"在": true, "于": true, "从": true, "到": true, "为": true, "对": true,
		"把": true, "被": true, "给": true, "让": true, "使": true, "叫": true,
		"这": true, "那": true, "哪": true, "些": true, "个": true, "来": true,
		"去": true, "上": true, "下": true, "里": true, "外": true, "中": true,
		"内": true, "间": true, "时": true, "候": true, "等": true, "啊": true,
		"呢": true, "吧": true, "吗": true, "呀": true, "哦": true, "嗯": true,
	}

		for _, keyword := range keywords {
		// 跳过长度小于2的词、纯数字、标点符号和无意义词汇
		if len(keyword) < 2 || skipWords[keyword] {
			continue
		}
		
		// 跳过纯数字（除非是特殊的数字组合）
		if isAllDigit(keyword) && len(keyword) < 3 {
			continue
		}
		
		// 跳过只包含标点符号的词
		if isPunctuation(keyword) {
			continue
		}
		
		result += "#" + keyword + " "
	}

	return strings.TrimSpace(result)
}

// isAllDigit 检查字符串是否全为数字
func isAllDigit(s string) bool {
	for _, char := range s {
		if char < '0' || char > '9' {
			return false
		}
	}
	return true
}

// isPunctuation 检查字符串是否只包含标点符号
func isPunctuation(s string) bool {
	punctuations := "[]()（）［］【】，。？！：；、\"'《》-—～~+=<>{}|\\/@#$%^&*`"
	for _, char := range s {
		found := false
		for _, punct := range punctuations {
			if char == punct {
				found = true
				break
			}
		}
		if !found {
			return false
		}
	}
	return true
}

//
//curl --location 'https://vendor-api.setapp.com/auth/v1/authorize' \
//--header 'accept: application/vnd.api+json' \
//--header 'content-type: application/vnd.api+json' \
//--data '{
//"client_id": "2862d686bf317a8cf76675b914795a6baf0f19fb2e10998a",
//"scope": "ai.openai",
//"session_token": "0c0392b3-ec86-4ce1-b336-80cb4fcef91e",
//"response_type": "code"
//}'
//
//curl --location 'https://vendor-api.setapp.com/auth/v1/token' \
//--header 'authorization: Basic Mjg2MmQ2ODZiZjMxN2E4Y2Y3NjY3NWI5MTQ3OTVhNmJhZjBmMTlmYjJlMTA5OThhOlBZTG1DamdyMk92R0wwU3VtT3llOTlqN3N3VnVDWmd4Umw0Q0hHcVQ=' \
//--header 'Content-Type: application/x-www-form-urlencoded' \
//--data-urlencode 'grant_type=authorization_code_by_setapp_library' \
//--data-urlencode 'code=def5020098666341dbc4686c1cf0a2f7de035f1646a05e93592e8da38912be1ebadec9ccc1c891af83c3910770adb985bdc5dbe3aeffe830a5db252e1168b472a0d29178869cda4f50b9c5ce0f24f91b2df628e1f75e41696a0f69adfbffceb15180ee70dbff6be9d17d4d6f06fd24a8277a60db2d7567f794aea2ecd759a85c3e9d295793a9cc34e507080a61c679b46e72cd717f45db8d5f1362c6876d3fac35645dba512d10d66db770cbbed774b0235bd77f5eacd4d8b0c16913059d35a3ebece98ab3f1bdcf067fd9d1e6992a367f96abceb8631dfb6ba5991049be483a63c754ca752a968a38f8823c0ac713057260b8cf4153aebd77efe5009af60c44f77db1deb73582484fecd867aa0041ba1deafe890986d835f71e25762ae85408b91008e43a2cdb4d2ce69d62dcc4635a7903f3819df015824975a7a981b9aefadd5d29c5851b6fb45e6c32c7265064055b6e75807ed3cd9aea9a0b9b57bd33335fa3c7d2845200bc50fb6370b3424d4e610015118ffda4788a3d82941fedefaab89b5f89e82aa9a33c4b983c9870f815160ab9573bb328616d3899a0c3cf'

//curl --location 'https://vendor-api.setapp.com/resource/v1/ai/openai/' \
//--header 'user-agent: Plus/131CFNetwork/1408.0.4Darwin/22.5.0' \
//--header 'openaipath: v1/chat/completions' \
//--header 'Authorization: Bearer def502007b78e6fcfeb460694b52e16598e5aed4b0aa59e97eebef933d77eb70e973b01e54b9ee2c172836a74645182953879d074fc458aa4d249ecce279b5dc988e077b25f67f5e1b8daf8427745cf976d3d0815edcf004e3d81bf8c5b67afdf6012bac948a5d1e8fab8093d4055a098d37d82ea59d9dd52594b32c8a0363f0561fe214251bdf5b3962e94336b404cebd628026c0b2a89175789d51d44657693c02a0a0969d578639646764285ed7c6038afb9a5eeac22a5d91ef9ffae839346ca58b94c5bed3573855a7fbf18119b089b4768c66cbf0a6189392f3d58a2214dbb32facb9163384502ec849f3bd4079caf5b4898731362ddb8ad313440ae86362053bf28cc4990b02cdd885dbca1c01446a01f5305cc3ab45ccca7629f351e1bf65d08844876a9a040af1d4b6e15868053e00b28c030cd39c1d5b121da09fb54f2023e87b914d5dc5b69769ee49dfab796cc671e9f1c81eba4b4bad61e39d32f78008183def97c692e96e98f60fbe9e86e365d4f05d45aced9fc1c01678ebb6934eb47902603fb5859146a0f8d44fe8dbbf7b0ffae9f81ec34ef8fcc6cd' \
//--header 'Content-Type: application/json' \
//--data '{
//		  "messages": [
//			{
//				"content": "《達蓋爾的旗幟》翻译成英文，叫什么好？",
//				"role": "user"
//			}
//		],
//		"model": "gpt-4",
//		"stream": false
//		}'
